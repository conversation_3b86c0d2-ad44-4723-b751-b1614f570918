package de.sarocesch.randomspawn;

import com.mojang.logging.LogUtils;
import de.sarocesch.randomspawn.commands.RandomSpawnCommands;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.events.PlayerEvents;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import org.slf4j.Logger;

public class SarosRandomSpawn implements ModInitializer
{
    // Define mod id in a common place for everything to reference
    public static final String MODID = "randomspawn";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    /**
     * Logs a debug message if debug mode is enabled in the configuration.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void debug(String message, Object... args) {
        if (de.sarocesch.randomspawn.config.RandomSpawnConfig.debugMode) {
            LOGGER.info(message, args);
        }
    }

    /**
     * Logs an info message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void info(String message, Object... args) {
        LOGGER.info(message, args);
    }

    /**
     * Logs a warning message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void warn(String message, Object... args) {
        LOGGER.warn(message, args);
    }

    /**
     * Logs an error message regardless of debug mode.
     *
     * @param message The message to log
     * @param args The arguments for the message format
     */
    public static void error(String message, Object... args) {
        LOGGER.error(message, args);
    }

    @Override
    public void onInitialize() {
        info("Initializing Saro's Random Spawn Mod");

        // Initialize configuration
        RandomSpawnConfig.initialize();
        info("Configuration initialized");

        // Register command callback
        CommandRegistrationCallback.EVENT.register((dispatcher, registryAccess, environment) -> {
            debug("Registering commands");
            RandomSpawnCommands.register(dispatcher);
        });

        // Register player events
        PlayerEvents.register();
        info("Player events registered for random teleportation");

        info("Saro's Random Spawn Mod initialized");
    }
}

{
	"schemaVersion": 1,
	"id": "randomspawn",
	"version": "${version}",
	"name": "<PERSON><PERSON>'s Random Spawn",
	"description": "A Fabric mod that teleports players to random locations when they join the server for the first time or on every join (configurable).",
	"authors": [
		"Sarocesch"
	],
	"contact": {
		"homepage": "https://fabricmc.net/",
		"sources": "https://github.com/FabricMC/fabric-example-mod"
	},
	"license": "CC0-1.0",
	"icon": "assets/randomspawn/icon.png",
	"environment": "*",
	"entrypoints": {
		"main": [
			"de.sarocesch.randomspawn.SarosRandomSpawn"
		]
	},
	"mixins": [
		"randomspawn.mixins.json"
	],
	"depends": {
		"fabricloader": ">=0.16.14",
		"minecraft": "~1.21.1",
		"java": ">=21",
		"fabric-api": "*"
	},
}
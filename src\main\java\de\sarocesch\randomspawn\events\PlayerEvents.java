package de.sarocesch.randomspawn.events;

import de.sarocesch.randomspawn.SarosRandomSpawn;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.util.TeleportationHelper;
import net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents;
import net.minecraft.text.Text;
import net.minecraft.server.network.ServerPlayerEntity;

public class PlayerEvents {

    /**
     * Register player event callbacks
     */
    public static void register() {
        ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            onPlayerLoggedIn(handler.getPlayer());
        });
    }

    private static void onPlayerLoggedIn(ServerPlayerEntity player) {
        String playerName = player.getName().getString();

        // Check if player is in the exception list
        boolean isInExceptionList = RandomSpawnConfig.playerExceptions.contains(playerName);

        // Determine if we should teleport based on whitelist/blacklist setting
        boolean shouldTeleport = RandomSpawnConfig.useWhitelist ? isInExceptionList : !isInExceptionList;

        // For now, let's use a simpler approach - always teleport on first join
        // TODO: Implement proper persistent data storage for tracking first join
        boolean isFirstJoin = true;

        if (shouldTeleport && (isFirstJoin || RandomSpawnConfig.teleportOnEveryJoin)) {
            SarosRandomSpawn.info("Teleporting player {} to random location", playerName);

            // TODO: Mark player as teleported in persistent data

            // Teleport the player to a random location
            TeleportationHelper.teleportToRandomLocation(player);

            // Send teleport message if enabled
            if (RandomSpawnConfig.showTeleportMessage) {
                player.sendMessage(Text.literal(RandomSpawnConfig.teleportMessage), false);
            }
        }
    }
}

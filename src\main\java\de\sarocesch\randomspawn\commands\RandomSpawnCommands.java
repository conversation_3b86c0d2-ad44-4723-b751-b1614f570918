package de.sarocesch.randomspawn.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import de.sarocesch.randomspawn.SarosRandomSpawn;
import de.sarocesch.randomspawn.config.RandomSpawnConfig;
import de.sarocesch.randomspawn.util.TeleportationHelper;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.command.CommandManager;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.text.Text;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.Collection;

/**
 * Registers and handles commands for the RandomSpawn mod.
 */
public class RandomSpawnCommands {

    /**
     * Registers all commands for the mod.
     *
     * @param dispatcher The command dispatcher to register commands with
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher) {
        dispatcher.register(
            CommandManager.literal("newspawn")
                .requires(source -> source.hasPermissionLevel(2)) // Requires permission level 2 (op)
                .then(CommandManager.argument("players", EntityArgumentType.players())
                    .executes(context -> respawnPlayers(context.getSource(), EntityArgumentType.getPlayers(context, "players")))
                )
                .executes(context -> respawnPlayer(context.getSource(), context.getSource().getPlayerOrThrow()))
        );

        SarosRandomSpawn.info("Registered RandomSpawn commands");
    }

    /**
     * Teleports multiple players to random locations.
     *
     * @param source The command source
     * @param players Collection of players to teleport
     * @return The number of players successfully teleported
     */
    private static int respawnPlayers(ServerCommandSource source, Collection<ServerPlayerEntity> players) {
        int successCount = 0;
        final int totalCount = players.size();

        for (ServerPlayerEntity player : players) {
            if (respawnPlayer(source, player) == 1) {
                successCount++;
            }
        }

        // Summary message for multiple players
        if (totalCount > 1) {
            final int finalSuccessCount = successCount;
            if (successCount == totalCount) {
                source.sendFeedback(() -> Text.literal("Successfully teleported all " + totalCount + " players"), true);
            } else {
                source.sendFeedback(() -> Text.literal("Teleported " + finalSuccessCount + " out of " + totalCount + " players"), true);
            }
        }

        return successCount;
    }

    /**
     * Teleports a player to a random location.
     *
     * @param source The command source
     * @param player The player to teleport
     * @return 1 if successful, 0 otherwise
     */
    private static int respawnPlayer(ServerCommandSource source, ServerPlayerEntity player) {
        // Get the player's name
        String playerName = player.getName().getString();

        // Teleport the player - in this version teleportTo is void, so we assume it always works
        TeleportationHelper.teleportToRandomLocation(player);

        // Notify the command source (only for single player teleports or self-teleport)
        if (player == source.getEntity() || source.getWorld().getPlayers().size() <= 2) {
            source.sendFeedback(() -> Text.literal("Teleported " + playerName + " to a random location"), true);
        }

        // Notify the player if they're not the command source
        if (player != source.getEntity() && RandomSpawnConfig.showTeleportMessage) {
            player.sendMessage(Text.literal(RandomSpawnConfig.teleportMessage), false);
        }

        return 1;
    }
}

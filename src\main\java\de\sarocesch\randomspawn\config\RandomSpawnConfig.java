package de.sarocesch.randomspawn.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import de.sarocesch.randomspawn.SarosRandomSpawn;
import net.fabricmc.loader.api.FabricLoader;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

public class RandomSpawnConfig {
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final Path CONFIG_PATH = FabricLoader.getInstance().getConfigDir().resolve("randomspawn.json");

    private static ConfigData configData = new ConfigData();

    // Configuration data class
    public static class ConfigData {
        // General settings
        public int minDistance = 500;
        public int maxDistance = 2000;
        public boolean teleportOnEveryJoin = false;
        public boolean allowNetherTeleport = false;
        public boolean allowEndTeleport = false;

        // Message settings
        public boolean showTeleportMessage = false;
        public String teleportMessage = "You have been teleported to a random location!";

        // Safety settings
        public boolean safeTeleport = true;
        public boolean spawnHighWithSlowFalling = false;

        // Debug settings
        public boolean debugMode = false;

        // Player exception settings
        public boolean useWhitelist = false;
        public List<String> playerExceptions = Arrays.asList("ExamplePlayer1", "ExamplePlayer2");
    }

    // Static accessors for config values
    public static int minDistance = 500;
    public static int maxDistance = 2000;
    public static boolean teleportOnEveryJoin = false;
    public static boolean allowNetherTeleport = false;
    public static boolean allowEndTeleport = false;
    public static boolean showTeleportMessage = false;
    public static String teleportMessage = "You have been teleported to a random location!";
    public static boolean safeTeleport = true;
    public static boolean spawnHighWithSlowFalling = false;
    public static boolean debugMode = false;
    public static boolean useWhitelist = false;
    public static List<String> playerExceptions = Arrays.asList("ExamplePlayer1", "ExamplePlayer2");

    /**
     * Initialize the configuration system
     */
    public static void initialize() {
        loadConfig();
    }

    /**
     * Load configuration from file
     */
    private static void loadConfig() {
        try {
            if (Files.exists(CONFIG_PATH)) {
                String json = Files.readString(CONFIG_PATH);
                configData = GSON.fromJson(json, ConfigData.class);
                if (configData == null) {
                    configData = new ConfigData();
                }
            } else {
                configData = new ConfigData();
                saveConfig();
            }
            updateStaticValues();
        } catch (IOException e) {
            SarosRandomSpawn.error("Failed to load config: {}", e.getMessage());
            configData = new ConfigData();
            updateStaticValues();
        }
    }

    /**
     * Save configuration to file
     */
    private static void saveConfig() {
        try {
            Files.createDirectories(CONFIG_PATH.getParent());
            String json = GSON.toJson(configData);
            Files.writeString(CONFIG_PATH, json);
        } catch (IOException e) {
            SarosRandomSpawn.error("Failed to save config: {}", e.getMessage());
        }
    }

    /**
     * Update static values from config data
     */
    private static void updateStaticValues() {
        minDistance = configData.minDistance;
        maxDistance = configData.maxDistance;
        teleportOnEveryJoin = configData.teleportOnEveryJoin;
        allowNetherTeleport = configData.allowNetherTeleport;
        allowEndTeleport = configData.allowEndTeleport;
        showTeleportMessage = configData.showTeleportMessage;
        teleportMessage = configData.teleportMessage;
        safeTeleport = configData.safeTeleport;
        spawnHighWithSlowFalling = configData.spawnHighWithSlowFalling;
        debugMode = configData.debugMode;
        useWhitelist = configData.useWhitelist;
        playerExceptions = configData.playerExceptions;
    }
}
